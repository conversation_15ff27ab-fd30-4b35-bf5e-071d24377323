# Pac-Man Multi-Agent System

A sophisticated Pac-Man implementation featuring autonomous agents, advanced AI behaviors, and comprehensive debugging tools. This project transforms the classic Pac-Man game into a multi-agent system where all entities operate autonomously using intelligent decision-making algorithms.

## 🎮 Game Features

### Multi-Agent System
- **Autonomous Pac-Man**: Rule-based AI that makes intelligent decisions without user input
- **Smart Ghost AI**: Four distinct ghost personalities with different chase and scatter behaviors
- **Priority-Based Decision Making**: Advanced pathfinding with weighted priorities for different objectives

### Enhanced Pac-Man Abilities
- **Teleportation System**: Limited-use teleport ability (5 teleports per game) with visual feedback
- **Intelligent Pathfinding**: Prioritizes avoiding ghosts, chasing frightened ghosts, and collecting pellets
- **Adaptive Detection**: Detection radius increases with each death for improved survival
- **Visual Feedback**: Color-coded teleport effects and on-screen ability counters

### Audio System
- **Complete Sound Integration**: Authentic Pac-Man sound effects
- **Smart Audio Management**: Prevents overlapping sounds and manages audio timing
- **Sound Effects Include**:
  - Game start/beginning
  - Pellet chomping
  - Ghost eating
  - Fruit collection
  - Death sequence
  - Level intermission

## 🛠️ Technical Architecture

### File Structure
The project uses a dual-file system:
- **Original Files** (`run.py`, `pacman.py`, `ghosts.py`): Manual control versions
- **System Files** (`system_*.py`): Autonomous multi-agent versions

### Key Components
- `system_run.py`: Main game controller with debugging features
- `system_pacman.py`: Autonomous Pac-Man with advanced AI
- `system_ghosts.py`: Intelligent ghost behaviors
- `system_entity.py`: Base entity class with enhanced capabilities
- `sounds.py`: Comprehensive audio management system

## 🎯 AI Behavior System

### Pac-Man Intelligence
1. **Priority System**:
   - Priority 0: Chase frightened ghosts (highest)
   - Priority 1: Collect pellets
   - Priority 2: Avoid normal ghosts
   - Priority 3: Random movement (fallback)

2. **Advanced Detection**:
   - `detectRadius`: For normal ghost avoidance
   - `detectFrightRadius`: For frightened ghost detection
   - `checkRadius`: For pellet detection
   - `teleportRadius`: For emergency teleportation

3. **Intelligent Movement**:
   - Reverses direction when detecting ghosts ahead
   - Prioritizes paths with pellets
   - Ignores returning/spawning ghosts
   - Uses teleportation as last resort

### Ghost Behaviors
- **Blinky (Red)**: Direct chase behavior
- **Pinky (Pink)**: Ambush tactics
- **Inky (Cyan)**: Complex positioning relative to Blinky
- **Clyde (Orange)**: Scatter behavior when close to Pac-Man

## 🔧 Debug Mode Features

Toggle debug mode with the `D` key to access:

### Visual Debug Tools
- **N**: Show/hide node network
- **T**: Display check radius for all entities
- **Y**: Show collision radius
- **R**: Display entity radius
- **G**: Show detection radius with directional indicators

### Speed Controls
- **Right Arrow**: Double game speed
- **Left Arrow**: Halve game speed
- **Real-time Speed Display**: Shows current speed multiplier

### Debug Information
- On-screen debug status indicator
- Active debug options display
- Speed control feedback
- Entity radius visualizations

## 🎮 Controls

### Standard Mode
- **Space**: Pause/unpause game
- **D**: Toggle debug mode

### Debug Mode (when active)
- **N, T, Y, R, G**: Toggle various visual debug overlays
- **Left/Right Arrows**: Adjust game speed
- **P**: Toggle between manual and autonomous ghost control (planned feature)

## 🚀 Getting Started

### Prerequisites
- Python 3.x
- Pygame library

### Installation
```bash
# Clone the repository
git clone [repository-url]

# Install dependencies
pip install pygame

# Run the autonomous version
python system_run.py

# Or run the manual version
python run.py
```

### Sound Setup
Ensure the `sounds/` directory contains all required audio files:
- `pacman_beginning.wav`
- `pacman_chomp.wav`
- `pacman_death.wav`
- `pacman_eatfruit.wav`
- `pacman_eatghost.wav`
- `pacman_extrapac.wav`
- `pacman_intermission.wav`

## 📊 Game Mechanics

### Scoring System
- Small pellets: 10 points
- Power pellets: 50 points
- Ghosts: 200, 400, 800, 1600 points (doubles each ghost)
- Fruits: Varies by level

### Lives and Progression
- Start with 5 lives
- Lose life when touching normal ghosts
- Detection radius increases with each death
- Teleport ability persists across lives

### Special Features
- **Teleport Counter**: Displayed at bottom of screen
- **Visual Feedback**: Color changes during teleportation
- **Smart Collision**: Separate radii for different interactions
- **Adaptive Difficulty**: Increasing detection ranges

## 🔬 Development Features

### Code Optimization
- Eliminated redundant calculations
- Efficient priority-based pathfinding
- Optimized collision detection
- Smart audio management

### Extensible Architecture
- Modular entity system
- Configurable AI parameters
- Easy debugging integration
- Scalable multi-agent framework

## 🎯 Future Enhancements

- Momentum bonus system
- Last-second dodge abilities
- Additional ghost personalities
- Machine learning integration
- Network multiplayer support

## 📝 License

This project is developed for educational purposes as part of a Multi-Agent Systems course.

---

**Note**: This implementation demonstrates advanced AI concepts including autonomous agents, priority-based decision making, and multi-agent coordination in a classic game environment.
