import pygame
import os

class SoundManager:
    def __init__(self):
        # Initialize pygame mixer
        pygame.mixer.init()

        # Dictionary to track if a sound is currently playing
        self.currently_playing = {}

        # Load all sound files
        self.sounds = {
            'beginning': self._load_sound('pacman_beginning.wav'),
            'chomp': self._load_sound('pacman_chomp.wav'),
            'death': self._load_sound('pacman_death.wav'),
            'eatfruit': self._load_sound('pacman_eatfruit.wav'),
            'eatghost': self._load_sound('pacman_eatghost.wav'),
            'extrapac': self._load_sound('pacman_extrapac.wav'),
            'intermission': self._load_sound('pacman_intermission.wav')
        }

        # All sounds use default volume

    def _load_sound(self, filename):
        """Load a sound file from the sounds directory"""
        path = os.path.join('sounds', filename)
        try:
            sound = pygame.mixer.Sound(path)
            # Initialize as not playing
            self.currently_playing[filename] = False
            return sound
        except pygame.error as e:
            print(f"Error loading sound {path}: {e}")
            return None

    def play(self, sound_name, force=False):
        """
        Play a sound by name

        Args:
            sound_name: Name of the sound to play
            force: If True, play even if already playing
        """
        if sound_name not in self.sounds or self.sounds[sound_name] is None:
            return

        # For chomp sound, don't play if already playing unless forced
        if sound_name == 'chomp' and self.currently_playing.get(f'pacman_{sound_name}.wav', False) and not force:
            return

        # Mark as playing
        self.currently_playing[f'pacman_{sound_name}.wav'] = True

        # Play the sound
        self.sounds[sound_name].play()

        # Set a timer to mark the sound as not playing after its duration
        duration = self.sounds[sound_name].get_length() * 1000  # Convert to milliseconds
        pygame.time.set_timer(pygame.USEREVENT + 1, int(duration), 1)

    def stop(self, sound_name):
        """Stop a sound by name"""
        if sound_name in self.sounds and self.sounds[sound_name] is not None:
            self.sounds[sound_name].stop()
            self.currently_playing[f'pacman_{sound_name}.wav'] = False



    def handle_sound_end_event(self, event):
        """Handle the sound end event to update playing status"""
        if event.type == pygame.USEREVENT + 1:
            # Reset all sounds to not playing
            # This is a simplification - in a more complex system, we'd track which sound triggered the event
            for sound_file in self.currently_playing:
                self.currently_playing[sound_file] = False
