import pygame
from pygame.locals import *
from constants import *
from system_pacman import <PERSON><PERSON>
from nodes import NodeGroup
from pellets import <PERSON>elletGroup
from ghosts import GhostGroup
from fruit import Fruit
from pauser import Pause
from text import TextGroup
from sprites import LifeSprites
from sprites import MazeSprites
from mazedata import MazeData
from sounds import SoundManager
from vector import Vector2

class GameController(object):
    def __init__(self):
        pygame.init()
        self.screen = pygame.display.set_mode(SCREENSIZE, 0, 32)
        self.background = None
        self.background_norm = None
        self.background_flash = None
        self.clock = pygame.time.Clock()
        self.fruit = None
        self.pause = Pause(True)
        self.level = 0
        self.lives = 5
        self.score = 0
        self.textgroup = TextGroup()
        self.lifesprites = LifeSprites(self.lives)
        self.flashBG = False
        self.flashTime = 0.2
        self.flashTimer = 0
        self.fruitCaptured = []
        self.fruitNode = None
        self.mazedata = MazeData()
        self.sound_manager = SoundManager()

        # Game speed control (default is 1.0 - normal speed)
        self.speed_multiplier = 1.0
        self.fps = 30

        # Debugging flags
        self.debug_mode = False
        self.show_nodes = False
        self.show_check_radius = False
        self.show_collide_radius = False
        self.show_radius = False
        self.show_detect_radius = False

    def setBackground(self):
        self.background_norm = pygame.surface.Surface(SCREENSIZE).convert()
        self.background_norm.fill(BLACK)
        self.background_flash = pygame.surface.Surface(SCREENSIZE).convert()
        self.background_flash.fill(BLACK)
        self.background_norm = self.mazesprites.constructBackground(self.background_norm, self.level%5)
        self.background_flash = self.mazesprites.constructBackground(self.background_flash, 5)
        self.flashBG = False
        self.background = self.background_norm

    def startGame(self):
        self.mazedata.loadMaze(self.level)
        self.mazesprites = MazeSprites(self.mazedata.obj.name+".txt", self.mazedata.obj.name+"_rotation.txt")
        self.setBackground()
        self.nodes = NodeGroup(self.mazedata.obj.name+".txt")
        self.mazedata.obj.setPortalPairs(self.nodes)
        self.mazedata.obj.connectHomeNodes(self.nodes)
        self.pacman = Pacman(self.nodes.getNodeFromTiles(*self.mazedata.obj.pacmanStart))
        self.pellets = PelletGroup(self.mazedata.obj.name+".txt")
        self.ghosts = GhostGroup(self.nodes.getStartTempNode(), self.pacman)

        self.ghosts.pinky.setStartNode(self.nodes.getNodeFromTiles(*self.mazedata.obj.addOffset(2, 3)))
        self.ghosts.inky.setStartNode(self.nodes.getNodeFromTiles(*self.mazedata.obj.addOffset(0, 3)))
        self.ghosts.clyde.setStartNode(self.nodes.getNodeFromTiles(*self.mazedata.obj.addOffset(4, 3)))
        self.ghosts.setSpawnNode(self.nodes.getNodeFromTiles(*self.mazedata.obj.addOffset(2, 3)))
        self.ghosts.blinky.setStartNode(self.nodes.getNodeFromTiles(*self.mazedata.obj.addOffset(2, 0)))

        self.nodes.denyHomeAccess(self.pacman)
        self.nodes.denyHomeAccessList(self.ghosts)
        self.ghosts.inky.startNode.denyAccess(RIGHT, self.ghosts.inky)
        self.ghosts.clyde.startNode.denyAccess(LEFT, self.ghosts.clyde)
        self.mazedata.obj.denyGhostsAccess(self.ghosts, self.nodes)

        # Play beginning sound when game starts
        self.sound_manager.play('beginning')

    def startGame_old(self):
        self.mazedata.loadMaze(self.level)#######
        self.mazesprites = MazeSprites("maze1.txt", "maze1_rotation.txt")
        self.setBackground()
        self.nodes = NodeGroup("maze1.txt")
        self.nodes.setPortalPair((0,17), (27,17))
        homekey = self.nodes.createHomeNodes(11.5, 14)
        self.nodes.connectHomeNodes(homekey, (12,14), LEFT)
        self.nodes.connectHomeNodes(homekey, (15,14), RIGHT)
        self.pacman = Pacman(self.nodes.getNodeFromTiles(15, 26))
        self.pellets = PelletGroup("maze1.txt")
        self.ghosts = GhostGroup(self.nodes.getStartTempNode(), self.pacman)
        self.ghosts.blinky.setStartNode(self.nodes.getNodeFromTiles(2+11.5, 0+14))
        self.ghosts.pinky.setStartNode(self.nodes.getNodeFromTiles(2+11.5, 3+14))
        self.ghosts.inky.setStartNode(self.nodes.getNodeFromTiles(0+11.5, 3+14))
        self.ghosts.clyde.setStartNode(self.nodes.getNodeFromTiles(4+11.5, 3+14))
        self.ghosts.setSpawnNode(self.nodes.getNodeFromTiles(2+11.5, 3+14))

        self.nodes.denyHomeAccess(self.pacman)
        self.nodes.denyHomeAccessList(self.ghosts)
        self.nodes.denyAccessList(2+11.5, 3+14, LEFT, self.ghosts)
        self.nodes.denyAccessList(2+11.5, 3+14, RIGHT, self.ghosts)
        self.ghosts.inky.startNode.denyAccess(RIGHT, self.ghosts.inky)
        self.ghosts.clyde.startNode.denyAccess(LEFT, self.ghosts.clyde)
        self.nodes.denyAccessList(12, 14, UP, self.ghosts)
        self.nodes.denyAccessList(15, 14, UP, self.ghosts)
        self.nodes.denyAccessList(12, 26, UP, self.ghosts)
        self.nodes.denyAccessList(15, 26, UP, self.ghosts)



    def update(self):
        # Apply speed multiplier to dt
        dt = self.clock.tick(self.fps) / 1000.0 * self.speed_multiplier
        self.textgroup.update(dt)
        self.pellets.update(dt)
        if not self.pause.paused:
            self.ghosts.update(dt)
            if self.fruit is not None:
                self.fruit.update(dt)
            self.checkPelletEvents()
            self.checkGhostEvents()
            self.checkFruitEvents()

        if self.pacman.alive:
            if not self.pause.paused:
                # Check teleport status before update
                teleport_was_active = hasattr(self.pacman, 'teleport_active') and self.pacman.teleport_active

                # Pass pellet list and ghost list directly to Pacman's update method
                self.pacman.update(dt, self.pellets.pelletList, self.ghosts)

                # Check if teleport just finished
                if teleport_was_active and hasattr(self.pacman, 'teleport_active') and not self.pacman.teleport_active:
                    # Display a message about the teleport
                    self.textgroup.addText("TELEPORTED!", TEAL, 175, 15, 8, time=1)
                    remaining = self.pacman.teleports_remaining
                    self.textgroup.addText(f"{remaining} TELEPORTS LEFT", TEAL, 170, 27, 8, time=1)
        else:
            # Only update Pacman's sprites when he's dead, not his position
            self.pacman.sprites.update(dt)

        if self.flashBG:
            self.flashTimer += dt
            if self.flashTimer >= self.flashTime:
                self.flashTimer = 0
                if self.background == self.background_norm:
                    self.background = self.background_flash
                else:
                    self.background = self.background_norm

        afterPauseMethod = self.pause.update(dt)
        if afterPauseMethod is not None:
            afterPauseMethod()
        self.checkEvents()
        self.render()

    def checkEvents(self):
        for event in pygame.event.get():
            if event.type == QUIT:
                exit()
            elif event.type == KEYDOWN:
                if event.key == K_SPACE:
                    if self.pacman.alive:
                        self.pause.setPause(playerPaused=True)
                        if not self.pause.paused:
                            self.textgroup.hideText()
                            self.showEntities()
                        else:
                            self.textgroup.showText(PAUSETXT)
                            self.hideEntities()
                # Debug mode toggle with 'd' key
                elif event.key == K_d:
                    self.debug_mode = not self.debug_mode
                    if not self.debug_mode:
                        # Reset all debug flags when exiting debug mode
                        self.show_nodes = False
                        self.show_check_radius = False
                        self.show_collide_radius = False
                        self.show_radius = False
                        self.show_detect_radius = False

                # Only process these keys if in debug mode
                if self.debug_mode:
                    # Toggle node display with 'n' key
                    if event.key == K_n:
                        self.show_nodes = not self.show_nodes

                    # Toggle check radius display with 't' key
                    elif event.key == K_t:
                        self.show_check_radius = not self.show_check_radius
                        if self.show_check_radius:
                            self.show_collide_radius = False
                            self.show_radius = False

                    # Toggle collide radius display with 'y' key
                    elif event.key == K_y:
                        self.show_collide_radius = not self.show_collide_radius
                        if self.show_collide_radius:
                            self.show_check_radius = False
                            self.show_radius = False

                    # Toggle regular radius display with 'r' key
                    elif event.key == K_r:
                        self.show_radius = not self.show_radius
                        if self.show_radius:
                            self.show_check_radius = False
                            self.show_collide_radius = False
                            self.show_detect_radius = False

                    # Toggle detect radius display with 'g' key
                    elif event.key == K_g:
                        self.show_detect_radius = not self.show_detect_radius
                        if self.show_detect_radius:
                            self.show_check_radius = False
                            self.show_collide_radius = False
                            self.show_radius = False

                    # Speed control with arrow keys
                    elif event.key == K_RIGHT:
                        # Double the game speed
                        self.speed_multiplier *= 2.0
                        self.textgroup.addText(f"SPEED: {self.speed_multiplier}x", GREEN, 175, 50, 8, time=1)
                    elif event.key == K_LEFT:
                        # Halve the game speed
                        self.speed_multiplier /= 2.0
                        # Ensure speed doesn't get too slow
                        if self.speed_multiplier < 0.25:
                            self.speed_multiplier = 0.25
                        self.textgroup.addText(f"SPEED: {self.speed_multiplier}x", GREEN, 175, 50, 8, time=1)

            # Handle sound end events
            elif event.type == pygame.USEREVENT + 1:
               self.sound_manager.handle_sound_end_event(event)

    def checkPelletEvents(self):
        pellet = self.pacman.eatPellets(self.pellets.pelletList)
        if pellet:
            self.pellets.numEaten += 1
            self.updateScore(pellet.points)
            # Play chomp sound when eating pellets
            self.sound_manager.play('chomp')
            if self.pellets.numEaten == 30:
                self.ghosts.inky.startNode.allowAccess(RIGHT, self.ghosts.inky)
            if self.pellets.numEaten == 70:
                self.ghosts.clyde.startNode.allowAccess(LEFT, self.ghosts.clyde)
            self.pellets.pelletList.remove(pellet)
            if pellet.name == POWERPELLET:
                self.ghosts.startFreight()
            if self.pellets.isEmpty():
                self.flashBG = True
                self.hideEntities()
                self.sound_manager.play('intermission')
                self.pause.setPause(pauseTime=3, func=self.nextLevel)

    def checkGhostEvents(self):
        for ghost in self.ghosts:
            if self.pacman.collideGhost(ghost):
                if ghost.mode.current is FREIGHT:
                    self.pacman.visible = False
                    ghost.visible = False
                    self.updateScore(ghost.points)
                    self.textgroup.addText(str(ghost.points), WHITE, ghost.position.x, ghost.position.y, 8, time=1)
                    self.ghosts.updatePoints()
                    # Play eat ghost sound
                    self.sound_manager.play('eatghost')
                    self.pause.setPause(pauseTime=1, func=self.showEntities)
                    ghost.startSpawn()
                    self.nodes.allowHomeAccess(ghost)
                elif ghost.mode.current is not SPAWN:
                    if self.pacman.alive:
                        self.lives -=  1
                        self.lifesprites.removeImage()
                        self.pacman.die()

                        # No longer increasing detection radius when Pacman loses a life
                        # self.pacman.detectRadius *= 1.5

                        # No longer displaying message about increased radius
                        # self.textgroup.addText("DETECTION RADIUS", GREEN, 175, 15, 8, time=2)
                        # self.textgroup.addText("INCREASED BY 1.5x", GREEN, 170, 27, 8, time=2)

                        # Play death sound and wait for it to finish before resetting
                        self.sound_manager.play('death')
                        self.ghosts.hide()
                        # Get the death sound duration and add a small delay
                        death_sound_duration = self.sound_manager.sounds['death'].get_length()
                        death_sound_duration = 2  # Use a fixed delay instead
                        if self.lives <= 0:
                            self.textgroup.showText(GAMEOVERTXT)
                            self.pause.setPause(pauseTime=death_sound_duration + 1, func=self.restartGame)
                        else:
                            self.textgroup.showText(READYTXT)
                            self.pause.setPause(pauseTime=death_sound_duration + 1, func=self.resetLevel)

    def checkFruitEvents(self):
        if self.pellets.numEaten == 50 or self.pellets.numEaten == 140:
            if self.fruit is None:
                self.fruit = Fruit(self.nodes.getNodeFromTiles(9, 20), self.level)
                print(self.fruit)
        if self.fruit is not None:
            if self.pacman.collideCheck(self.fruit):
                self.updateScore(self.fruit.points)
                self.textgroup.addText(str(self.fruit.points), WHITE, self.fruit.position.x, self.fruit.position.y, 8, time=1)
                # Play eat fruit sound
                self.sound_manager.play('eatfruit')
                fruitCaptured = False
                for fruit in self.fruitCaptured:
                    if fruit.get_offset() == self.fruit.image.get_offset():
                        fruitCaptured = True
                        break
                if not fruitCaptured:
                    self.fruitCaptured.append(self.fruit.image)
                self.fruit = None
            elif self.fruit.destroy:
                self.fruit = None

    def showEntities(self):
        self.pacman.visible = True
        self.ghosts.show()

    def hideEntities(self):
        self.pacman.visible = False
        self.ghosts.hide()

    def nextLevel(self):
        self.showEntities()
        self.level += 1
        self.pause.paused = True
        self.startGame()
        self.textgroup.updateLevel(self.level)
        # Play intermission sound between levels
        self.sound_manager.play('intermission')

    def restartGame(self):
        self.lives = 5
        self.level = 0
        self.pause.paused = True
        self.fruit = None
        self.startGame()
        # Reset radius values to their initial values
        if hasattr(self.pacman, 'checkRadius'):
            self.pacman.checkRadius = 20
        if hasattr(self.pacman, 'detectFrightRadius'):
            self.pacman.detectFrightRadius = 15

        # Reset teleport counter to 5 when starting a new game
        if hasattr(self.pacman, 'teleports_remaining'):
            self.pacman.teleports_remaining = 5
        self.score = 0
        self.textgroup.updateScore(self.score)
        self.textgroup.updateLevel(self.level)
        self.textgroup.showText(READYTXT)
        self.lifesprites.resetLives(self.lives)
        self.fruitCaptured = []
        # Play beginning sound
        self.sound_manager.play('beginning')

    def resetLevel(self):
        self.pause.paused = True
        self.pacman.reset()
        self.ghosts.reset()
        self.fruit = None
        self.textgroup.showText(READYTXT)
        # Play beginning sound when level resets after death
        self.beginning_sound_playing = True
        duration = self.sound_manager.play('beginning')
        # Set a timer to automatically unpause after the beginning sound
        if duration:
            pygame.time.set_timer(pygame.USEREVENT + 2, int(duration * 1000), 1)
        # Use a fixed delay instead
        pygame.time.set_timer(pygame.USEREVENT + 2, 2000, 1)

    def updateScore(self, points):
        self.score += points
        self.textgroup.updateScore(self.score)

    def render(self):
        self.screen.blit(self.background, (0, 0))

        # Render nodes if debug mode is active and nodes are enabled
        if self.debug_mode and self.show_nodes:
            self.nodes.render(self.screen)

        self.pellets.render(self.screen)
        if self.fruit is not None:
            self.fruit.render(self.screen)
        self.pacman.render(self.screen)
        self.ghosts.render(self.screen)

        # Render debug information if debug mode is active
        if self.debug_mode:
            self.render_debug_info()

        self.textgroup.render(self.screen)

        for i in range(len(self.lifesprites.images)):
            x = self.lifesprites.images[i].get_width() * i
            y = SCREENHEIGHT - self.lifesprites.images[i].get_height()
            self.screen.blit(self.lifesprites.images[i], (x, y))

        for i in range(len(self.fruitCaptured)):
            x = SCREENWIDTH - self.fruitCaptured[i].get_width() * (i+1)
            y = SCREENHEIGHT - self.fruitCaptured[i].get_height()
            self.screen.blit(self.fruitCaptured[i], (x, y))

        # Display teleport counter
        if hasattr(self.pacman, 'teleports_remaining'):
            font = pygame.font.Font(None, 24)
            teleport_text = font.render(f"TELEPORTS: {self.pacman.teleports_remaining}", True, TEAL)
            self.screen.blit(teleport_text, (SCREENWIDTH // 2 - teleport_text.get_width() // 2, SCREENHEIGHT - 24))

        # Display debug mode status in the top center
        if self.debug_mode:
            font = pygame.font.Font(None, 24)
            debug_text = font.render("DEBUGGING MODE", True, RED)
            text_width = debug_text.get_width()
            self.screen.blit(debug_text, (SCREENWIDTH // 2 - text_width // 2 +15, 10))

            # Display active debug options
            small_font = pygame.font.Font(None, 16)
            active_options = []
            if self.show_nodes:
                active_options.append("N: Nodes")
            if self.show_check_radius:
                active_options.append("T: Check Radius")
            if self.show_collide_radius:
                active_options.append("Y: Collide Radius")
            if self.show_radius:
                active_options.append("R: Radius")
            if self.show_detect_radius:
                active_options.append("G: Detect Radius")

            if active_options:
                options_text = small_font.render(" | ".join(active_options), True, WHITE)
                self.screen.blit(options_text, (SCREENWIDTH // 2 - options_text.get_width() // 2, 30))

            # Display current game speed
            speed_text = small_font.render(f"SPEED: {self.speed_multiplier:.2f}x | ← SLOWER | FASTER →", True, GREEN)
            self.screen.blit(speed_text, (SCREENWIDTH // 2 - speed_text.get_width() // 2, 45))

        pygame.display.update()

    def render_debug_info(self):
        # Render check radius
        if self.show_check_radius:
            self.render_check_radius()

        # Render collide radius
        if self.show_collide_radius:
            self.render_collide_radius()

        # Render regular radius
        if self.show_radius:
            self.render_radius()

        # Render detect radius
        if self.show_detect_radius:
            self.render_detect_radius()

    def render_check_radius(self):
        # Render Pacman's check radius
        if hasattr(self.pacman, 'checkRadius'):
            p = self.pacman.position.asInt()
            pygame.draw.circle(self.screen, GREEN, p, int(self.pacman.checkRadius), 1)

        # Render pellets' check radius
        for pellet in self.pellets.pelletList:
            if hasattr(pellet, 'checkRadius'):
                adjust = Vector2(TILEWIDTH, TILEHEIGHT) / 2
                p = (pellet.position + adjust).asInt()
                # Use TEAL instead of BLUE since BLUE is not defined in constants
                pygame.draw.circle(self.screen, TEAL, p, int(pellet.checkRadius), 1)

        # Render ghosts' check radius if they have it
        for ghost in self.ghosts:
            if hasattr(ghost, 'checkRadius'):
                p = ghost.position.asInt()
                pygame.draw.circle(self.screen, TEAL, p, int(ghost.checkRadius), 1)

    def render_collide_radius(self):
        # Render Pacman's collide radius
        p = self.pacman.position.asInt()
        pygame.draw.circle(self.screen, YELLOW, p, int(self.pacman.collideRadius), 1)

        # Render pellets' collide radius
        for pellet in self.pellets.pelletList:
            adjust = Vector2(TILEWIDTH, TILEHEIGHT) / 2
            p = (pellet.position + adjust).asInt()
            pygame.draw.circle(self.screen, WHITE, p, int(pellet.collideRadius), 1)

        # Render ghosts' collide radius
        for ghost in self.ghosts:
            p = ghost.position.asInt()
            pygame.draw.circle(self.screen, ghost.color, p, int(ghost.collideRadius), 1)

    def render_radius(self):
        # Render Pacman's radius
        p = self.pacman.position.asInt()
        pygame.draw.circle(self.screen, YELLOW, p, self.pacman.radius, 1)

        # Render pellets' radius
        for pellet in self.pellets.pelletList:
            adjust = Vector2(TILEWIDTH, TILEHEIGHT) / 2
            p = (pellet.position + adjust).asInt()
            pygame.draw.circle(self.screen, WHITE, p, pellet.radius, 1)

        # Render ghosts' radius
        for ghost in self.ghosts:
            p = ghost.position.asInt()
            pygame.draw.circle(self.screen, ghost.color, p, ghost.radius, 1)

    def render_detect_radius(self):
        # Render Pacman's detect radius
        if hasattr(self.pacman, 'detectRadius'):
            p = self.pacman.position.asInt()
            pygame.draw.circle(self.screen, RED, p, int(self.pacman.detectRadius), 1)

            # Also render a line in the direction Pacman is moving to show detection range
            ahead_position = self.pacman.position + self.pacman.directions[self.pacman.direction] * self.pacman.detectRadius
            pygame.draw.line(self.screen, RED, p, ahead_position.asInt(), 1)

        # Render Pacman's detect fright radius (for frightened ghosts)
        if hasattr(self.pacman, 'detectFrightRadius'):
            p = self.pacman.position.asInt()
            # Use TEAL instead of BLUE since BLUE is not defined in constants
            pygame.draw.circle(self.screen, TEAL, p, int(self.pacman.detectFrightRadius), 1)

        # Render ghosts' detect radius if they have it
        for ghost in self.ghosts:
            if hasattr(ghost, 'detectRadius'):
                p = ghost.position.asInt()
                pygame.draw.circle(self.screen, PINK, p, int(ghost.detectRadius), 1)


if __name__ == "__main__":
    game = GameController()
    game.startGame()
    while True:
        game.update()



